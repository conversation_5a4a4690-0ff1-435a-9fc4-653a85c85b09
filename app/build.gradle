apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-kapt'

android {
    signingConfigs {
        config {
            keyAlias 'epson'
            keyPassword 'epson@com'
            storeFile file('/Users/<USER>/Library/CloudStorage/OneDrive-Pactera/04_Gientech/01_项目管/EPSON/2025 Projector Data Update/Code/V1.0/Android APP/1.0/app/epson.jks')
            storePassword 'epson@'
        }
    }
    compileSdkVersion 31
    defaultConfig {
        applicationId "com.epson.htdc"
        minSdkVersion 21
        targetSdkVersion 31
        versionCode 2
        versionName "1.5.7"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.config
        }
    }
    kotlinOptions {
        freeCompilerArgs += ["-Xopt-in=kotlin.RequiresOptIn"]
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'com.android.support:appcompat-v7:27.1.0'
    implementation 'com.alibaba:fastjson:1.2.46'
    implementation 'com.android.support:recyclerview-v7:27.1.0'
    implementation 'com.android.support:design:27.1.0'
    implementation 'com.github.arcadefire:nice-spinner:1.3.1'
}

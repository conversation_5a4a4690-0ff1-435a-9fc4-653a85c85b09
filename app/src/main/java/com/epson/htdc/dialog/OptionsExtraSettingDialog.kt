package com.epson.htdc.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.support.v4.app.DialogFragment
import android.view.*
import com.epson.htdc.R
import com.epson.htdc.config.*
import com.epson.htdc.databinding.DialogOptionsExtraSettingBinding

class OptionsExtraSettingDialog : DialogFragment() {

    private lateinit var editTexts: ArrayList<AutoEditText>
    private var onOptionsExtraSettingListener: (() -> Unit)? = null

    companion object {
        fun newInstance(type: OptionExtraSettingType) = OptionsExtraSettingDialog().apply {
            arguments = Bundle().apply { putString(GL.ARGUMENTS_KEY, type.name) }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialog.window?.requestFeature(Window.FEATURE_NO_TITLE)
        return inflater.inflate(R.layout.dialog_options_extra_setting, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val unit = GL.getUnitName(Options.unit)
        val base = if (Options.unit == TypeUnit.INCH) Options.unit.value * 12 else Options.unit.value
        mToolbar.setNavigationOnClickListener { dismissAllowingStateLoss() }
        editTexts = arrayListOf(mOptionExtraSettingCellingHeight, mOptionExtraSettingFloorHeight)
        arrayListOf(mOptionExtraSettingCellingHeightUnit, mOptionExtraSettingFloorHeightUnit).forEach { it.text = unit }
        mOptionExtraSettingCellingHeight.setText(display(Options.extraCellingHeight))
        mOptionExtraSettingFloorHeight.setText(display(Options.extraFloorToScreenHeight))
        mOptionExtraSettingSubmit.setOnClickListener {
            var value = mOptionExtraSettingCellingHeight.text.toString().toFloatOrNull() ?: -1F
            Options.extraCellingHeight = if (value < 0) Options.extraCellingHeight else value * base
            value = mOptionExtraSettingFloorHeight.text.toString().toFloatOrNull() ?: -1F
            Options.extraFloorToScreenHeight = if (value < 0) Options.extraFloorToScreenHeight else value * base
            onOptionsExtraSettingListener?.invoke()
            dismissAllowingStateLoss()
        }
        arguments?.getString(GL.ARGUMENTS_KEY)?.let { type ->
            when (OptionExtraSettingType.valueOf(type)) {
                OptionExtraSettingType.CELLING -> {
                    mOptionExtraSettingCellingHeight.requestFocus()
                    mOptionExtraSettingCellingHeight.setSelection(mOptionExtraSettingCellingHeight.text.length)
                }
                OptionExtraSettingType.FLOOR -> {
                    mOptionExtraSettingFloorHeight.requestFocus()
                    mOptionExtraSettingFloorHeight.setSelection(mOptionExtraSettingFloorHeight.text.length)
                }
            }
        }
    }

    override fun onStart() {
        super.onStart()
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog.window?.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT)
        dialog.window?.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        dialog.window?.attributes = dialog.window?.attributes?.apply { dimAmount = 0F }
        dialog.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
        dialog.setOnKeyListener { _, keyCode, _ ->
            if (keyCode == KeyEvent.KEYCODE_BACK) {
                dismissAllowingStateLoss()
                true
            } else false
        }
    }

    fun setOptionsSettingListener(listener: () -> Unit): OptionsExtraSettingDialog {
        onOptionsExtraSettingListener = listener
        return this
    }
}

enum class OptionExtraSettingType {
    CELLING, FLOOR
}
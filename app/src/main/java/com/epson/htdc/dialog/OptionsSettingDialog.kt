package com.epson.htdc.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.support.v4.app.DialogFragment
import android.text.SpannableStringBuilder
import android.view.*
import android.widget.EditText
import com.epson.htdc.R
import com.epson.htdc.config.*
import com.epson.htdc.config.GL.INCH
import kotlinx.android.synthetic.main.dialog_options_setting.*
import kotlin.math.atan
import kotlin.math.cos
import kotlin.math.roundToInt
import kotlin.math.sin


class OptionsSettingDialog : DialogFragment() {

    private lateinit var editTexts: ArrayList<AutoEditText>
    private var onOptionsSettingListener: (() -> Unit)? = null

    companion object {
        fun newInstance(type: OptionSettingType) = OptionsSettingDialog().apply {
            arguments = Bundle().apply { putString(GL.ARGUMENTS_KEY, type.name) }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialog.window?.requestFeature(Window.FEATURE_NO_TITLE)
        return inflater.inflate(R.layout.dialog_options_setting, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mToolbar.setNavigationOnClickListener { dismissAllowingStateLoss() }
        val unit = GL.getUnitName(Options.unit)
        editTexts = arrayListOf(mOptionSettingScreenSize, mOptionSettingScreenWidth, mOptionSettingScreenHeight, mOptionSettingThrowDistance)
        arrayListOf(mOptionSettingHeightUnit, mOptionSettingThrowDistanceUnit).forEach { it.text = unit }
        editTexts.forEach { v -> v.setOnTextChangedListener { onTextChangedCallBack(v) } }
        mOptionSettingSubmit.setOnClickListener {
            onOptionsSettingListener?.invoke()
            dismissAllowingStateLoss()
        }
        mOptionSettingScreenSizeTitle.text = getString(R.string.screen_size_title, Options.len.ssMin, Options.len.ssMax)
        if (Options.optionsUpdateType == Options.UpdateType.ALL) {
            val distance = if (Options.throwDistance.first > 0) Options.throwDistance.first else Options.throwDistance.second
            mOptionSettingThrowDistance.requestFocus()
            mOptionSettingThrowDistance.setText(if (distance > 0) com.epson.htdc.config.distance(distance) else "")
            mOptionSettingThrowDistance.setSelection(mOptionSettingThrowDistance.text.length)
        } else {
            val parameter = if (Options.optionsUpdateType == Options.UpdateType.ORIGINAL) Options.original else Options.calculated
            mOptionSettingScreenSize.requestFocus()
            mOptionSettingScreenSize.setText(if (parameter.size > 0) parameter.size.smart() else "")
            mOptionSettingScreenSize.setSelection(mOptionSettingScreenSize.text.length)
        }
        arguments?.getString(GL.ARGUMENTS_KEY)?.let { type ->
            when (OptionSettingType.valueOf(type)) {
                OptionSettingType.SIZE -> {
                    mOptionSettingScreenSize.requestFocus()
                    mOptionSettingScreenSize.setSelection(mOptionSettingScreenSize.text.length)
                }
                OptionSettingType.WIDTH_AND_HEIGHT -> {
                    mOptionSettingScreenWidth.requestFocus()
                    mOptionSettingScreenWidth.setSelection(mOptionSettingScreenWidth.text.length)
                }
                OptionSettingType.THROW_DISTANCE -> {
                    mOptionSettingThrowDistance.requestFocus()
                    mOptionSettingThrowDistance.setSelection(mOptionSettingThrowDistance.text.length)
                }
            }
        }
    }

    private fun onScreenSizeChanged(size: Float) {
        if (size.roundToInt() < Options.len.ssMin || size.roundToInt() > Options.len.ssMax) {
            mOptionSettingScreenSize.setTextColor(Color.RED)
            onResetChanged(mOptionSettingScreenSize)
        } else {
            mOptionSettingScreenSize.setTextColor(mOptionSettingScreenSize.currentHintTextColor)
            val width = size * cos(atan(Options.screenAspectRatio)) * INCH
            val height = width * Options.screenAspectRatio
            mOptionSettingScreenWidth.text = SpannableStringBuilder(display(width))
            mOptionSettingScreenHeight.text = SpannableStringBuilder(display(height))
            calcThrowDistance(size, width, height)
        }
    }

    private fun onScreenWidthChanged(width: Float) {
        val size = width / cos(atan(Options.screenAspectRatio)) / INCH
        if (size.roundToInt() < Options.len.ssMin || size.roundToInt() > Options.len.ssMax) {
            mOptionSettingScreenWidth.setTextColor(Color.RED)
            onResetChanged(mOptionSettingScreenWidth)
        } else {
            mOptionSettingScreenWidth.setTextColor(mOptionSettingScreenWidth.currentHintTextColor)
            val height = width * Options.screenAspectRatio
            mOptionSettingScreenSize.text = SpannableStringBuilder(size.smart())
            mOptionSettingScreenHeight.text = SpannableStringBuilder(display(height))
            calcThrowDistance(size, width, height)
        }
    }

    private fun onScreenHeightChanged(height: Float) {
        val size = height / sin(atan(Options.screenAspectRatio)) / INCH
        if (size.roundToInt() < Options.len.ssMin || size.roundToInt() > Options.len.ssMax) {
            mOptionSettingScreenHeight.setTextColor(Color.RED)
            onResetChanged(mOptionSettingScreenHeight)
        } else {
            mOptionSettingScreenHeight.setTextColor(mOptionSettingScreenHeight.currentHintTextColor)
            val width = height / Options.screenAspectRatio
            mOptionSettingScreenSize.text = SpannableStringBuilder(size.smart())
            mOptionSettingScreenWidth.text = SpannableStringBuilder(display(width))
            calcThrowDistance(size, width, height)
        }
    }


    private fun onThrowDistanceChanged(distance: Float) {
        Options.setDistance(distance.roundToInt())
        if (Options.original.size.roundToInt() < Options.len.ssMin || Options.original.size.roundToInt() > Options.len.ssMax) {
            mOptionSettingThrowDistance.setTextColor(Color.RED)
            onResetChanged(mOptionSettingThrowDistance)
        } else {
            mOptionSettingThrowDistance.setTextColor(mOptionSettingThrowDistance.currentHintTextColor)
            val parameter = if (Options.optionsUpdateType == Options.UpdateType.ORIGINAL) Options.original else Options.calculated
            mOptionSettingScreenSize.text = SpannableStringBuilder(if (Options.optionsUpdateType == Options.UpdateType.ALL) {
                display(Options.original.size) + " ~ " + display(Options.calculated.size)
            } else {
                display(parameter.size)
            })
            mOptionSettingScreenWidth.text = SpannableStringBuilder(if (Options.optionsUpdateType == Options.UpdateType.ALL) {
                display(Options.original.width) + " ~ " + display(Options.calculated.width)
            } else {
                display(parameter.width)
            })
            mOptionSettingScreenHeight.text = SpannableStringBuilder(if (Options.optionsUpdateType == Options.UpdateType.ALL) {
                display(Options.original.height) + " ~ " + display(Options.calculated.height)
            } else {
                display(parameter.height)
            })
        }
    }

    private fun calcThrowDistance(size: Float, width: Float, height: Float) {
        Options.setOriginal(size, width, height)
        mOptionSettingThrowDistance.text = SpannableStringBuilder(if (Options.throwDistance.first > 0 && Options.throwDistance.second > 0) {
            distance(Options.throwDistance.first) + " ~ " + distance(Options.throwDistance.second)
        } else {
            if (Options.throwDistance.first > 0) distance(Options.throwDistance.first) else distance(Options.throwDistance.second)
        })
    }

    private fun onResetChanged(view: EditText? = null) {
        editTexts.forEach { if (it.id != view?.id) it.text.clear() }
    }

    override fun onStart() {
        super.onStart()
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog.window?.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT)
        dialog.window?.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        dialog.window?.attributes = dialog.window?.attributes?.apply { dimAmount = 0F }
        dialog.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
        dialog.setOnKeyListener { _, keyCode, _ ->
            if (keyCode == KeyEvent.KEYCODE_BACK) {
                dismissAllowingStateLoss()
                true
            } else false
        }
    }

    fun setOptionsSettingListener(listener: () -> Unit): OptionsSettingDialog {
        onOptionsSettingListener = listener
        return this
    }

    private fun onTextChangedCallBack(view: EditText) {
        if (view.hasFocus()) {
            val value = view.text.toString().toFloatOrNull()
            if (value == null) {
                onResetChanged()
            } else {
                val unit = if (Options.unit == TypeUnit.INCH) GL.FT else Options.unit.value
                when (view.id) {
                    mOptionSettingScreenSize.id -> onScreenSizeChanged(value)
                    mOptionSettingScreenWidth.id -> onScreenWidthChanged(value * unit)
                    mOptionSettingScreenHeight.id -> onScreenHeightChanged(value * unit)
                    mOptionSettingThrowDistance.id -> onThrowDistanceChanged(value * unit)
                }
            }
        } else {
            view.setTextColor(view.currentHintTextColor)
        }
    }
}

enum class OptionSettingType {
    SIZE, WIDTH_AND_HEIGHT, THROW_DISTANCE
}
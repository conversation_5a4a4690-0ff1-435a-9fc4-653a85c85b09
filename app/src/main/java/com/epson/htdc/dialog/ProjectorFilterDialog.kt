package com.epson.htdc.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.support.v4.app.DialogFragment
import android.support.v7.widget.LinearLayoutManager
import android.text.Editable
import android.text.TextWatcher
import android.view.*
import android.widget.AdapterView
import android.widget.EditText
import com.epson.htdc.*
import com.epson.htdc.config.*
import com.epson.htdc.databinding.DialogProjectorFilterBinding
import kotlinx.android.synthetic.main.item_filter_resolution.view.*
import kotlin.math.roundToInt

class ProjectorFilterDialog : DialogFragment() {

    private var onFilterChangedListener: ((SearchTerms) -> Unit)? = null
    private lateinit var terms: SearchTerms

    companion object {
        fun newInstance(terms: SearchTerms, focus: Int) = ProjectorFilterDialog().apply {
            arguments = Bundle().apply {
                putParcelable(GL.ARGUMENTS_KEY, terms)
                putInt(GL.ARGUMENTS_KEY_EXTRA, focus)
            }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialog.window?.requestFeature(Window.FEATURE_NO_TITLE)
        return inflater.inflate(R.layout.dialog_projector_filter, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        terms = arguments!!.getParcelable(GL.ARGUMENTS_KEY)!!
        val focus = arguments!!.getInt(GL.ARGUMENTS_KEY_EXTRA, 0)

        mToolbar.setNavigationOnClickListener { dismissAllowingStateLoss() }

        //初始化亮度
        mFilterBrightnessSpinner.attachDataSource(resources.getStringArray(R.array.condition).toList())
        mFilterBrightnessSpinner.selectedIndex = (terms.brightness?.condition?.ordinal ?: mFilterBrightnessSpinner.setOnItemSelectedListener(object : AdapterView.OnItemSelectedListener {
            override fun onNothingSelected(parent: AdapterView<*>?) {}
            override fun onItemSelected(parent: AdapterView<*>, view: View?, position: Int, id: Long) {
                mFilterBrightnessMax.visibility = if (position == 3) View.VISIBLE else View.GONE
            }
        })) as Int
        mFilterBrightnessMax.visibility = if (mFilterBrightnessSpinner.selectedIndex == 3) View.VISIBLE else View.GONE
        mFilterBrightnessMin.setText(if (terms.brightness!!.min > 0) terms.brightness!!.min.toString() else "")
        mFilterBrightnessMin.setSelection(mFilterBrightnessMin.text.length)
        mFilterBrightnessMax.setText(if (terms.brightness!!.max > 0) terms.brightness!!.max.toString() else "")
        mFilterBrightnessMax.setSelection(mFilterBrightnessMax.text.length)

        //初始化投影距离
        mFilterThrowDistanceUnitSpinner.attachDataSource(resources.getStringArray(R.array.unit).toList())
        mFilterThrowDistanceUnitSpinner.selectedIndex = terms.throwDistance!!.unit!!.ordinal
        mFilterThrowDistanceSpinner.attachDataSource(resources.getStringArray(R.array.condition).toList())
        mFilterThrowDistanceSpinner.selectedIndex = terms.throwDistance!!.condition!!.ordinal
        mFilterThrowDistanceSpinner.setOnItemSelectedListener(object : AdapterView.OnItemSelectedListener {
            override fun onNothingSelected(parent: AdapterView<*>?) {}
            override fun onItemSelected(parent: AdapterView<*>, view: View?, position: Int, id: Long) {
                mFilterThrowDistanceMax.visibility = if (position == 3) View.VISIBLE else View.GONE
            }
        })

        //初始化屏幕尺寸
        mFilterScreenSize.setText(if (terms.screenSize > 0) terms.screenSize.toString() else "")
        mFilterScreenSize.setSelection(mFilterScreenSize.text.length)
        mFilterScreenSize.addTextChangedListener(screenSizeCallback)
        screenSizeCallback.onTextChanged(mFilterScreenSize.text, 0, 0, 0)

        mFilterThrowDistanceMax.visibility = if (mFilterThrowDistanceSpinner.selectedIndex == 3) View.VISIBLE else View.GONE
        val min = if (mFilterThrowDistanceSpinner.selectedIndex == 2) terms.throwDistance!!.max else terms.throwDistance!!.min
        mFilterThrowDistanceMin.setText(if (min > 0) min.smart() else "")
        mFilterThrowDistanceMin.setSelection(mFilterThrowDistanceMin.text.length)
        mFilterThrowDistanceMax.setText(if (terms.throwDistance!!.max > 0 && terms.throwDistance!!.max < 1E6 && mFilterThrowDistanceSpinner.selectedIndex != 2) terms.throwDistance!!.max.smart() else "")
        mFilterThrowDistanceMax.setSelection(mFilterThrowDistanceMax.text.length)

        //初始化系列
        val dataSource = resources.getStringArray(R.array.category).toMutableList()
        mFilterCategoryList.layoutManager = LinearLayoutManager(activity)
        mFilterCategoryList.adapter = ListAdapter(activity!!, R.layout.item_filter_resolution, dataSource) { v, _, category ->
            v.mItemResolution.text = category
            v.mItemResolution.setTextColor(Color.parseColor(if (category == terms.category) "#5677FC" else "#606060"))
            v.mItemResolution.setCompoundDrawables(null, null, if (category == terms.category) context!!.getDrawable(R.drawable.ic_check)
                ?.apply {
                    setBounds(0, 0, intrinsicWidth, intrinsicHeight)
                } else null, null)
        }
        mFilterCategoryList.addOnItemTouchListener(object : OnRecyclerItemClickListener(mFilterCategoryList) {
            override fun onItemClick(position: Int) {
                if (terms.category == dataSource[position]) {
                    terms.category = ""
                } else {
                    terms.category = dataSource[position]
                }
                mFilterCategoryList.adapter.notifyItemRangeChanged(0, dataSource.size)
            }
        })
        mFilterCategoryList.isNestedScrollingEnabled = false

        //初始化分辨率
        mFilterResolutionList.layoutManager = LinearLayoutManager(activity)
        mFilterResolutionList.adapter = ListAdapter(activity!!, R.layout.item_filter_resolution, DB.resolutionList) { v, _, resolution ->
            v.mItemResolution.text = resolution
            v.mItemResolution.setTextColor(Color.parseColor(if (resolution == terms.resolution) "#5677FC" else "#606060"))
            v.mItemResolution.setCompoundDrawables(null, null, if (resolution == terms.resolution) context!!.getDrawable(R.drawable.ic_check)
                ?.apply {
                    setBounds(0, 0, intrinsicWidth, intrinsicHeight)
                } else null, null)
        }
        mFilterResolutionList.addOnItemTouchListener(object : OnRecyclerItemClickListener(mFilterResolutionList) {
            override fun onItemClick(position: Int) {
                if (terms.category == DB.resolutionList[position]) {
                    terms.resolution = ""
                } else {
                    terms.resolution = DB.resolutionList[position]
                }
                mFilterResolutionList.adapter.notifyItemRangeChanged(0, DB.resolutionList.size)
            }
        })
        mFilterResolutionList.isNestedScrollingEnabled = false

        if (focus == R.id.mFilterResolutionList) {
            mNestedScrollView.post {
                mNestedScrollView.scrollTo(0, mFilterResolutionList.top)
            }
        } else if (focus != 0) {
            view.findViewById<EditText>(focus).requestFocus()
        }

        //重置
        mFilterReset.setOnClickListener {
            val query = terms.query
            terms = SearchTerms()
            terms.query = query
            mFilterCategoryList.adapter.notifyItemRangeChanged(0, dataSource.size)
            mFilterResolutionList.adapter.notifyItemRangeChanged(0, DB.resolutionList.size)
            arrayListOf(mFilterBrightnessMin, mFilterBrightnessMax, mFilterScreenSize, mFilterThrowDistanceMin, mFilterBrightnessMax).forEach { it.text.clear() }
            onFilterChangedListener?.invoke(terms)
        }

        //提交
        mFilterSubmit.setOnClickListener { checkSearchTerms() }
    }

    private fun checkSearchTerms() {
        //亮度
        terms.brightness = mFilterBrightnessMin.text.toString().toFloatOrNull()?.let { it ->
            val min = it.roundToInt()
            when (mFilterBrightnessSpinner.selectedIndex) {
                0 -> Brightness(min, condition = Condition.Equal)
                1 -> Brightness(min, condition = Condition.Above)
                2 -> Brightness(min, condition = Condition.Below)
                else -> mFilterBrightnessMax.text.toString().toFloatOrNull().let { max ->
                    if (max != null && max > min) Brightness(min, max.roundToInt(), Condition.Between) else Brightness()
                }
            }
        } ?: Brightness()
        //屏幕尺寸
        terms.screenSize = mFilterScreenSize.text.toString().toIntOrNull() ?: 0
        //投影距离
        if (terms.screenSize > 0) {
            val unit = when (mFilterThrowDistanceUnitSpinner.selectedIndex) {
                1 -> TypeUnit.M
                2 -> TypeUnit.INCH
                else -> TypeUnit.CM
            }
            terms.throwDistance = mFilterThrowDistanceMin.text.toString().toFloatOrNull()?.let { min ->
                when (mFilterThrowDistanceSpinner.selectedIndex) {
                    0 -> ThrowDistance(min, min, condition = Condition.Equal, unit = unit)
                    1 -> ThrowDistance(min, 1E6.toFloat(), condition = Condition.Above, unit = unit)
                    2 -> ThrowDistance(0F, min, condition = Condition.Below, unit = unit)
                    else -> mFilterThrowDistanceMax.text.toString().toFloatOrNull().let { max ->
                        if (max != null && max > min) ThrowDistance(min, max, Condition.Between, unit = unit) else ThrowDistance()
                    }
                }
            } ?: ThrowDistance()
        } else {
            terms.throwDistance = ThrowDistance()
        }
        onFilterChangedListener?.invoke(terms)
        dismissAllowingStateLoss()
    }

    //屏幕尺寸回调
    private val screenSizeCallback = object : TextWatcher {
        override fun afterTextChanged(s: Editable?) {}
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            val isEnabled = s.toString().toIntOrNull() != null
            mFilterThrowDistanceMin.isEnabled = isEnabled
            mFilterThrowDistanceMax.isEnabled = isEnabled
            mFilterThrowDistanceSpinner.isEnabled = isEnabled
            mFilterThrowDistanceUnitSpinner.isEnabled = isEnabled
            if (isEnabled) {
                mFilterThrowDistanceMin.text.clear()
                mFilterThrowDistanceMax.text.clear()
                mFilterThrowDistanceTitle.text = "投影距离"
                mFilterThrowDistanceSpinner.alpha = 1F
                mFilterThrowDistanceUnitSpinner.alpha = 1F
            } else {
                mFilterThrowDistanceTitle.text = "投影距离（请先设置屏幕尺寸)"
                mFilterThrowDistanceSpinner.alpha = 0.5F
                mFilterThrowDistanceUnitSpinner.alpha = 0.5F
            }
        }
    }

    fun setFilterChangedListener(listener: (SearchTerms) -> Unit): ProjectorFilterDialog {
        onFilterChangedListener = listener
        return this
    }

    override fun onStart() {
        super.onStart()
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog.window?.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT)
        dialog.window?.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        if (arguments?.getInt(GL.ARGUMENTS_KEY_EXTRA, 0) != 0 && arguments?.getInt(GL.ARGUMENTS_KEY_EXTRA, 0) != R.id.mFilterResolutionList) {
            dialog.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
        }
        dialog.window?.attributes = dialog.window?.attributes?.apply { dimAmount = 0F }
        dialog.setOnKeyListener { _, keyCode, _ ->
            if (keyCode == KeyEvent.KEYCODE_BACK) {
                dismissAllowingStateLoss()
                true
            } else false
        }
    }
}
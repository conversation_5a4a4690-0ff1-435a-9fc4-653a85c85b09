package com.epson.htdc.dialog

import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.support.v4.app.DialogFragment
import android.view.*
import com.epson.htdc.R
import com.epson.htdc.config.GL
import com.epson.htdc.config.ModelTableEntity
import kotlinx.android.synthetic.main.dialog_projector_info.*


class ProjectorInfoDialog : DialogFragment() {

    companion object {
        fun newInstance(model: ModelTableEntity) = ProjectorInfoDialog().apply {
            arguments = Bundle().apply { putParcelable(GL.ARGUMENTS_KEY, model) }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.dialog_projector_info, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        arguments?.getParcelable<ModelTableEntity>(GL.ARGUMENTS_KEY)?.apply {
            mInfoTitle.text = model
            try {
                mInfoImage.setImageBitmap(resources.assets.open("images/${model?.replace(" *".toRegex(), "")}.png").use { BitmapFactory.decodeStream(it) })
            } catch (e: Exception) {
                mInfoImage.setImageResource(0)
            }
            mInfoCategory.text = category?.let { GL.getCategoryName(it) }
            mInfoColorBrightness.text = colorBrightness
            mInfoResolution.text = resolution
            mInfoContrastRatio.text = contrastRatio
            mInfoWeight.text = weight
            mInfoLaserLightSource.text = if (laserLightSource) "激光" else "灯泡"
            mInfoReplaceableLens.text = if (replaceableLens) "支持" else "不支持"
            mInfoFingerInteraction.text = if (fingerInteraction) "支持" else "不支持"
        }
        mInfoContentLayout.setOnClickListener { }
        mInfoLayout.setOnClickListener { dismissAllowingStateLoss() }
        mInfoCancel.setOnClickListener { dismissAllowingStateLoss() }
    }

    override fun onStart() {
        super.onStart()
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog.window?.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT)
        dialog.window?.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        dialog.window?.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        dialog.setOnKeyListener { _, keyCode, _ ->
            if (keyCode == KeyEvent.KEYCODE_BACK) {
                dismissAllowingStateLoss()
                true
            } else false
        }
    }
}
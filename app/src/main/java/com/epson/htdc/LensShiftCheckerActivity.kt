package com.epson.htdc

import android.os.Bundle
import android.support.v7.app.AppCompatActivity
import android.view.WindowManager
import com.epson.htdc.databinding.ActivityLensShiftCheckerBinding

class LensShiftCheckerActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        setContentView(R.layout.activity_lens_shift_checker)
        mToolbar.setNavigationOnClickListener { onBackPressed() }
        mLensShiftView.setOnShiftCallback { size, horizontal, vertical ->
            mLensShiftScreenSize.text = size
            mLensShiftHorizontal.text = horizontal
            mLensShiftVertical.text = vertical
        }
    }
}
package com.epson.htdc

import android.os.Bundle
import android.support.v7.app.AppCompatActivity
import android.support.v7.widget.LinearLayoutManager
import android.view.WindowManager
import com.epson.htdc.config.*
import kotlinx.android.synthetic.main.activity_lens_list.*
import kotlinx.android.synthetic.main.item_list.view.*

/**
 * Create by chimis(<EMAIL>) on 2018/2/8
 */
class LensListActivity : AppCompatActivity() {

    private lateinit var modelTable: ModelTableEntity

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        setContentView(R.layout.activity_lens_list)
        modelTable = intent.getParcelableExtra(GL.ARGUMENTS_KEY)!!
        val masterData = intent.getParcelableExtra<MasterDataEntity>(GL.ARGUMENTS_KEY_EXTRA)
        mToolbar.subtitle = "${modelTable.model} - ${modelTable.data?.let {
            Regex("""(?<=_).+""").find(
                it
            )?.value
        }}"
        mToolbar.setNavigationOnClickListener { onBackPressed() }
        mRecyclerView.layoutManager = LinearLayoutManager(this)
        val newIntent = intent.apply {
            setClass(this@LensListActivity, MainActivity::class.java)
            putExtra(GL.ARGUMENTS_KEY, modelTable)
        }
        if (masterData != null && masterData.lens!!.size == 1) {
            startActivity(newIntent.apply { putExtra(GL.ARGUMENTS_KEY_EXTRA, masterData) })
            finish()
        } else {
            if (masterData != null) {
                mRecyclerView.adapter = ListAdapter(this, R.layout.item_list, masterData.lens) { view, _, lens ->
                    view.mItemText.text = lens.name
                }
            }
            mRecyclerView.addOnItemTouchListener(object : OnRecyclerItemClickListener(mRecyclerView) {
                override fun onItemClick(position: Int) {
                    startActivity(newIntent.apply {
                        if (masterData != null) {
                            putExtra(GL.ARGUMENTS_KEY_EXTRA, masterData.copy(lens = mutableListOf(masterData.lens[position])))
                        }
                    })
                }
            })
        }
    }
}
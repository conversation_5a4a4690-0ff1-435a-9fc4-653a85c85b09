package com.epson.htdc

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.support.design.widget.Snackbar
import android.support.v7.app.AppCompatActivity
import android.view.KeyEvent
import android.view.View
import android.view.WindowManager
import android.widget.RadioButton
import com.epson.htdc.config.*
import com.epson.htdc.dialog.OptionExtraSettingType
import com.epson.htdc.dialog.OptionSettingType
import com.epson.htdc.dialog.OptionsExtraSettingDialog
import com.epson.htdc.dialog.OptionsSettingDialog
import kotlinx.android.synthetic.main.activity_main.*
import kotlin.math.max
import kotlin.math.roundToInt

class MainActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        setContentView(R.layout.activity_main)

        Options.init(this)

        updateOptionsLabel()

        mToolbar.subtitle = "${Options.model.model} - ${Options.len.name}"
        mToolbar.setNavigationOnClickListener { onBackPressed() }

        //这几个镜头下，投影距离title显示为L+D
        val searchStrings = listOf("ELPLX02S", "ELPLX01S", "ELPLX01", "ELPLX02", "ELPLX03")
        for (searchString in searchStrings) {
            if(Options.len.name?.contains(searchString, ignoreCase = true) == true) {
                mThrowDistanceTitle.text = "投影距离(L+D)"
                break
            }
        }

        (0 until mScreenAspectRatioGroup.childCount)
                .filter { mScreenAspectRatioGroup.getChildAt(it).tag.toString().toFloat() == Options.screenAspectRatio }
                .forEach { mScreenAspectRatioGroup.check(mScreenAspectRatioGroup.getChildAt(it).id) }

        mScreenAspectRatioGroup.setOnCheckedChangeListener { group, checkedId ->
            Options.setAspectRatio(group.findViewById<View>(checkedId).tag.toString().toFloat())
            updateOptionsLabel()
        }

        if (Options.isFlexCellingType) {
            mInstallationTypeGroup.check(R.id.mInstallationTypeCelling)
            mInstallationTypeDesktop.isEnabled = false
        } else {
            mInstallationTypeGroup.check(R.id.mInstallationTypeDesktop)
            mInstallationTypeDesktop.isEnabled = true
        }

        mLensShiftChecker.visibility = if (Options.isSupportLensChecker) View.VISIBLE else View.GONE

        mInstallationTypeGroup.setOnCheckedChangeListener { _, checkedId ->
            Options.installType = if (checkedId == mInstallationTypeDesktop.id) Options.InstallType.DESKTOP else Options.InstallType.CELLING
            updateOptionsLabel()
        }

        mUnitGroup.check(mUnitGroup.getChildAt(0).id)
        mUnitGroup.setOnCheckedChangeListener { group, checkedId ->
            Options.unit = TypeUnit.valueOf(group.findViewById<RadioButton>(checkedId).tag.toString())
            updateOptionsLabel()
        }

        arrayListOf(mOptionScreenSize, mOptionScreenWidthAndHeight, mThrowDistance).forEach { v ->
            v.setOnClickListener {
                OptionsSettingDialog.newInstance(when (v) {
                    mOptionScreenWidthAndHeight -> OptionSettingType.WIDTH_AND_HEIGHT
                    mThrowDistance -> OptionSettingType.THROW_DISTANCE
                    else -> OptionSettingType.SIZE
                }).setOptionsSettingListener {
                    updateOptionsLabel()
                }.show(supportFragmentManager, "OptionsSettingDialog")
            }
        }

        arrayListOf(mExtraOptionsCellingHeight, mExtraOptionsFloorToScreenHeight).forEach { v ->
            v.setOnClickListener {
                OptionsExtraSettingDialog.newInstance(if (v == mExtraOptionsCellingHeight) {
                    OptionExtraSettingType.CELLING
                } else {
                    OptionExtraSettingType.FLOOR
                }).setOptionsSettingListener {
                    updateOptionsLabel()
                }.show(supportFragmentManager, "OptionsExtraSettingDialog")
            }
        }

        mUseRoomInformationSwitch.setOnCheckedChangeListener { _, isChecked ->
            Options.isUsedRoomInformation = isChecked
            mExtraOptionsCellingHeight.isEnabled = isChecked
            mExtraOptionsFloorToScreenHeight.isEnabled = isChecked
            updateOptionsLabel()
        }

        mUseSettingPlateSwitchLayout.visibility = if (Options.isFlexCellingType) View.VISIBLE else View.GONE
        mUseSettingPlateSwitch.setOnCheckedChangeListener { _, isChecked ->
            Options.isUsedSettingPlate = isChecked
            updateOptionsLabel()
        }

        mExtraOptionsButton.setOnClickListener {
            if (Options.original.size <= 0 && Options.calculated.size <= 0) {
                Snackbar.make(it, "请先设置屏幕参数", Snackbar.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            if (mExtraOptionsLayout.visibility == View.VISIBLE) {
                mExtraOptionsLayout.visibility = View.GONE
                mExtraOptionsButton.text = "附加选项"
                mExtraOptionsButton.setTextColor(Color.parseColor("#757575"))
            } else {
                mExtraOptionsLayout.visibility = View.VISIBLE
                mExtraOptionsButton.text = "收起"
                mExtraOptionsButton.setTextColor(Color.parseColor("#5677FC"))
            }
        }

        mLensShiftChecker.setOnClickListener { startActivity(Intent(this, LensShiftCheckerActivity::class.java)) }
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK && mExtraOptionsLayout.visibility == View.VISIBLE) {
            mExtraOptionsButton.performClick()
            return true
        }
        return super.onKeyUp(keyCode, event)
    }

    /**
     * 更新结果显示
     */
    @SuppressLint("SetTextI18n")
    private fun updateOptionsLabel() {
        updateProjectorView()
        //设置屏幕参数
        if (Options.original.size <= 0 && Options.calculated.size <= 0) return
        val parameter = if (Options.optionsUpdateType == Options.UpdateType.ORIGINAL || Options.optionsUpdateType == Options.UpdateType.ALL) Options.original else Options.calculated
        mOptionScreenSize.text = if (Options.optionsUpdateType == Options.UpdateType.ALL) {
            getString(R.string.option_screen_size_both, Options.original.size.smart(), Options.calculated.size.smart())
        } else {
            getString(R.string.option_screen_size, parameter.size.smart())
        }
        val unit = if (Options.unit != TypeUnit.INCH) GL.getUnitName(Options.unit) else ""
        mOptionScreenWidthAndHeight.text = if (Options.optionsUpdateType == Options.UpdateType.ALL) {
            getString(R.string.option_screen_width_and_height_both, display(Options.original.width), display(Options.calculated.width), display(Options.original.height), display(Options.calculated.height), unit)
        } else {
            getString(R.string.option_screen_width_and_height, display(parameter.width), display(parameter.height), unit)
        }
        if (mUseRoomInformationSwitch.isChecked && max(Options.original.height, Options.calculated.height) + Options.extraFloorToScreenHeight > Options.extraCellingHeight) {
            mOptionScreenWidthAndHeight.paint.isFakeBoldText = true
            mOptionScreenWidthAndHeight.setTextColor(Color.WHITE)
            mOptionScreenWidthAndHeight.setBackgroundColor(Color.RED)
        } else {
            mOptionScreenWidthAndHeight.paint.isFakeBoldText = false
            mOptionScreenWidthAndHeight.setTextColor(mOptionScreenWidthAndHeight.currentHintTextColor)
            mOptionScreenWidthAndHeight.setBackgroundColor(Color.TRANSPARENT)
        }
        //计算py
        var projectorOffset = parameter.height / 2 - parameter.currentHeight * Options.len.offset - Options.len.bToA
        if (Options.isUsedSettingPlate && Options.installType == Options.InstallType.CELLING) {
            projectorOffset += Options.master.pOffset
        }
        Options.py = if (projectorOffset < 0) 68 else 30
        //设置照度
        mIllumination.text = if (Options.optionsUpdateType == Options.UpdateType.ALL) {
            getString(R.string.option_illumination_both,
                    (Options.model.brightness / (Options.original.width.roundToInt() * Options.original.height.roundToInt() * 0.0001)).roundToInt(),
                    (Options.model.brightness / (Options.calculated.width.roundToInt() * Options.calculated.height.roundToInt() * 0.0001)).roundToInt())
        } else {
            getString(R.string.option_illumination, (Options.model.brightness / (parameter.width.roundToInt() * parameter.height.roundToInt() * 0.0001)).roundToInt())
        }
        //设置投影距离（L）
        mThrowDistance.text = if (Options.throwDistance.first > 0 && Options.throwDistance.second > 0) {
            getString(R.string.throw_distance_unit, distance(Options.throwDistance.first), distance(Options.throwDistance.second), unit)
        } else {
            getString(R.string.throw_distance_offset_unit, if (Options.throwDistance.first > 0) distance(Options.throwDistance.first) else distance(Options.throwDistance.second), unit)
        }
        //设置距屏幕边缘的距离（H）
        val isCelling = Options.installType == Options.InstallType.CELLING
        var flag = false
        var offsetMin: Float = (Options.original.height / 2 - Options.original.currentHeight * Options.len.offset - Options.len.bToA).toFloat()
        if (Options.isUsedSettingPlate && isCelling) {
            offsetMin += Options.master.pOffset
        }
        if (Options.isUsedRoomInformation) {
            mDistanceOffsetTitle.text = getString(if (isCelling) R.string.distance_from_celling else R.string.distance_from_floor)
            offsetMin += if (!isCelling) {
                Options.extraFloorToScreenHeight
            } else {
                Options.extraCellingHeight - Options.extraFloorToScreenHeight - Options.original.height
            }
        } else {
            mDistanceOffsetTitle.text = getString(R.string.distance_from_screen_edge)
            if (offsetMin < 0) {
                flag = true
                offsetMin *= -1
            }
        }
        var offsetMax = 0F
        if (Options.optionsUpdateType != Options.UpdateType.ALL && Options.master.dZoom <= 1) {
            mDistanceOffset.text = getString(R.string.throw_distance_offset_unit, display(offsetMin), unit)
        } else {
            offsetMax = (Options.calculated.height / 2 - Options.calculated.currentHeight * Options.len.offset * Options.master.dZoom - Options.len.bToA).toFloat()
            if (Options.isUsedSettingPlate && isCelling) {
                offsetMax += Options.master.pOffset
            }
            if (Options.isUsedRoomInformation) {
                offsetMax += if (!isCelling) {
                    Options.extraFloorToScreenHeight
                } else {
                    Options.extraCellingHeight - Options.extraFloorToScreenHeight - Options.calculated.height
                }
            } else {
                if (flag && offsetMax < 0) {
                    offsetMax *= -1
                } else if (flag) {
                    offsetMin *= -1
                }
            }
            mDistanceOffset.text = getString(R.string.throw_distance_unit, display(offsetMin), display(offsetMax), unit)
        }

        if (Options.isUsedRoomInformation && (offsetMin < 0 || offsetMax < 0)) {
            mDistanceOffset.paint.isFakeBoldText = true
            if (offsetMin < 0 && offsetMax < 0) {
                mDistanceOffset.setTextColor(Color.WHITE)
                mDistanceOffset.setBackgroundColor(Color.RED)
            } else {
                mDistanceOffset.setTextColor(Color.BLACK)
                mDistanceOffset.setBackgroundColor(Color.YELLOW)
            }
        } else {
            mDistanceOffset.paint.isFakeBoldText = false
            mDistanceOffset.setTextColor(mDistanceOffset.currentHintTextColor)
            mDistanceOffset.setBackgroundColor(Color.TRANSPARENT)
        }

        mExtraOptionsCellingHeight.text = display(Options.extraCellingHeight) + " " + unit
        mExtraOptionsFloorToScreenHeight.text = display(Options.extraFloorToScreenHeight) + " " + unit

        updateProjectorView()
    }

    private fun updateProjectorView() {
        val lensType = Options.len.type % 100
        val pjType = (Options.len.type - lensType) / 100
        val tPy = if (Options.len.offset > 0) Options.py else 0
        val installType = if (Options.installType == Options.InstallType.DESKTOP) 1 else -1
        if (mProjectorView.pjType == -1 || mProjectorView.lensType == -1) {
            mProjectorView.setType(pjType, lensType)
            mProjectorView.setScreenCenter(if (pjType == 1) 110F else 70F, if (pjType == 1) 148F else 120F)
        }
        mProjectorView.locate(if (pjType == 1) 200F else 250F, if (pjType == 1) 70F else 120F + installType * tPy)
        mProjectorView.invalidate()
    }

    override fun onDestroy() {
        super.onDestroy()
        Options.destroy()
    }
}

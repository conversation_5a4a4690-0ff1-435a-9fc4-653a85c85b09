package com.epson.htdc

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Parcel
import android.os.Parcelable
import android.support.v7.app.AppCompatActivity
import android.support.v7.widget.LinearLayoutManager
import android.support.v7.widget.Toolbar
import android.text.Editable
import android.text.SpannableString
import android.text.TextWatcher
import android.text.style.AbsoluteSizeSpan
import android.view.MenuItem
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import com.epson.htdc.config.*
import com.epson.htdc.config.GL.CM
import com.epson.htdc.config.GL.FT
import com.epson.htdc.dialog.ProjectorFilterDialog
import com.epson.htdc.dialog.ProjectorInfoDialog
import kotlinx.android.synthetic.main.activity_projector_list.*
import kotlinx.android.synthetic.main.item_projector.view.*

/**
 * Create by chimis(<EMAIL>) on 2018/2/8
 */
class ProjectorListActivity : AppCompatActivity(), Toolbar.OnMenuItemClickListener, View.OnClickListener {

    private val dataSource = arrayListOf<ModelTableEntity>()
    private val modelLensMap = mutableMapOf<String, MasterDataEntity>()
    private var terms = SearchTerms()

    private lateinit var imm: InputMethodManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        setContentView(R.layout.activity_projector_list)
        DB.init(this)
        imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        var count = 0;
        var missingDataList: MutableList<String> = ArrayList()

        dataSource.addAll(DB.modelTableList.apply {
            forEach {
                val map = DB.findMasterData(it)
                if (map is MasterDataEntity) {
                    it.model?.let { it1 -> modelLensMap.put(it1, map) }
//                    modelLensMap[it.model] = map
                } else {
                    it.data?.let { it1 -> missingDataList.add(it1) }
                    count++
                }
                println("--------------------------------------"+count)
            }
            println("*********************************************"+missingDataList)
        })
        mToolbar.inflateMenu(R.menu.menu_projector_list)
        mToolbar.setOnMenuItemClickListener(this)
        mSearchBox.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                terms.query = s?.toString() ?: ""
                search()
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })
        mRecyclerView.layoutManager = LinearLayoutManager(this)
        mRecyclerView.adapter = ListAdapter(this, R.layout.item_projector, dataSource) { view, position, modelTable ->
            view.mItemText.text = modelTable.model
            view.mItemText.setOnClickListener {
                startActivity(Intent(this@ProjectorListActivity, LensListActivity::class.java).apply {
                    val keyValue = dataSource[position];
                    val keyExtraValue = modelLensMap[dataSource[position].model];
                    val modellensMap__ = modelLensMap
                    putExtra(GL.ARGUMENTS_KEY, dataSource[position])
                    putExtra(GL.ARGUMENTS_KEY_EXTRA, modelLensMap[dataSource[position].model])
                    if (terms.screenSize > 0) {
                        putExtra(GL.ARGUMENTS_KEY_SCREEN_SIZE, terms.screenSize.toFloat())
                    }
                })
            }
            view.mItemAction.setOnClickListener {
                ProjectorInfoDialog.newInstance(dataSource[position]).show(supportFragmentManager, "info")
            }
        }
        for (index in 0 until mFilterLayout.childCount) {
            mFilterLayout.getChildAt(index).setOnClickListener(this)
        }
    }

    override fun onClick(v: View?) {
        ProjectorFilterDialog.newInstance(terms, when (v) {
            mFilterBrightness -> R.id.mFilterBrightnessMin
            mFilterScreenSize -> R.id.mFilterScreenSize
            mFilterThrowDistance -> if (terms.screenSize > 0) R.id.mFilterThrowDistanceMin else 0
            mFilterResolution -> R.id.mFilterResolutionList
            else -> 0
        }).setFilterChangedListener {
            terms = it
            //设置系列Label
            if (terms.category?.isNotBlank() == true) {
                mFilterCategory.text = SpannableString("系列\n${terms.category}").apply {
                    setSpan(AbsoluteSizeSpan(8, true), 0, 3, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)
                }
            } else {
                mFilterCategory.text = "系列"
            }
            //设置亮度Label
            if (terms.brightness?.isNotNull() == true) {
                mFilterBrightness.text = SpannableString(when (terms.brightness!!.condition) {
                    Condition.Equal -> "亮度\n${terms.brightness!!.min}lm"
                    Condition.Above -> "亮度\n≥ ${terms.brightness!!.min}lm"
                    Condition.Below -> "亮度\n≤ ${terms.brightness!!.min}lm"
                    else -> "亮度\n${terms.brightness!!.min} ~ ${terms.brightness!!.max}lm"
                }).apply {
                    setSpan(AbsoluteSizeSpan(8, true), 0, 3, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)
                }
            } else {
                mFilterBrightness.text = "亮度"
            }
            //设置分辨率Label
            if (terms.resolution?.isNotBlank() == true) {
                mFilterResolution.text = SpannableString("分辨率\n${terms.resolution}").apply {
                    setSpan(AbsoluteSizeSpan(8, true), 0, 4, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)
                }
            } else {
                mFilterResolution.text = "分辨率"
            }
            //设置屏幕尺寸Label
            if (terms.screenSize > 0) {
                mFilterScreenSize.text = SpannableString("屏幕尺寸\n${terms.screenSize}\"").apply {
                    setSpan(AbsoluteSizeSpan(8, true), 0, 5, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)
                }
            } else {
                mFilterScreenSize.text = "屏幕尺寸"
            }
            //设置投影距离Label
            if (terms.throwDistance?.isNotNull() == true) {
                mFilterThrowDistance.text = SpannableString(when (terms.throwDistance!!.condition) {
                    Condition.Equal -> "投影距离\n${terms.throwDistance!!.min.smart()} ${terms.throwDistance!!.unit?.name?.toLowerCase()}"
                    Condition.Above -> "投影距离\n≥ ${terms.throwDistance!!.min.smart()} ${terms.throwDistance!!.unit?.name?.toLowerCase()}"
                    Condition.Below -> "投影距离\n≤ ${terms.throwDistance!!.max.smart()} ${terms.throwDistance!!.unit?.name?.toLowerCase()}"
                    else -> "投影距离\n${terms.throwDistance!!.min.smart()} ~ ${terms.throwDistance!!.max.smart()} ${terms.throwDistance!!.unit?.name?.toLowerCase()}"
                }).apply {
                    setSpan(AbsoluteSizeSpan(8, true), 0, 5, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)
                }
            } else {
                mFilterThrowDistance.text = "投影距离"
            }
            search()
        }.show(supportFragmentManager, ProjectorListActivity::class.java.simpleName)
    }

    override fun onMenuItemClick(item: MenuItem?): Boolean {
        when (item?.itemId) {
            R.id.menu_search -> {
                mSearchBox.visibility = View.VISIBLE
                mSearchBox.requestFocus()
                imm.showSoftInput(mSearchBox, InputMethodManager.SHOW_FORCED)
                item.isVisible = false
                mToolbar.menu.findItem(R.id.menu_cancel).isVisible = true
            }
            R.id.menu_cancel -> {
                if (mSearchBox.text.isNullOrBlank()) {
                    mSearchBox.visibility = View.GONE
                    mSearchBox.clearFocus()
                    imm.hideSoftInputFromWindow(mSearchBox.windowToken, 0)
                    item.isVisible = false
                    mToolbar.menu.findItem(R.id.menu_search).isVisible = true
                } else {
                    mSearchBox.text.clear()
                }
            }
            R.id.menu_filter -> {
                mFilterCategory.performClick()
                mFilterLayout.visibility = View.VISIBLE
            }
            R.id.menu_close -> {
                mFilterLayout.visibility = View.GONE
                item.isVisible = false
                mToolbar.menu.findItem(R.id.menu_filter).isVisible = true
            }
        }
        return false
    }

    private fun search() {
        var modelTableList = mutableListOf<ModelTableEntity>()
        modelTableList.addAll(DB.modelTableList)
        if (terms.query?.isNotBlank() == true) {
            modelTableList = modelTableList.filter { terms.query?.let { it1 -> it.model!!.contains(it1, true) }!! }.toMutableList()
        }
        if (terms.category?.isNotBlank() == true) {
            modelTableList = modelTableList.filter {
                it.category!!.toLowerCase() == GL.getCategoryValue(terms.category!!).toLowerCase()
            }.toMutableList()
        }
        if (terms.brightness?.isNotNull() == true) {
            val brightness = terms.brightness
            if (brightness != null) {
                modelTableList = when (brightness.condition) {
                    Condition.Equal -> modelTableList.filter { it.brightness == brightness.min }
                    Condition.Above -> modelTableList.filter { it.brightness >= brightness.min }
                    Condition.Below -> modelTableList.filter { it.brightness <= brightness.min }
                    else -> modelTableList.filter { it.brightness >= brightness.min && it.brightness <= brightness.max }
                }.toMutableList()
            }
        }
        if (terms.resolution?.isNotBlank() == true) {
            modelTableList = modelTableList.filter {
                it.data!!.replace("(.+_)|(([0-9]|\\.)+\$)".toRegex(), "").toUpperCase() == terms.resolution!!.toUpperCase()
            }.toMutableList()
        }
        modelLensMap.clear()
        if (terms.screenSize > 0) {
            modelTableList = modelTableList.filter {
                var master = DB.findMasterData(it)!!
                master = master.copy(lens = master.lens.filter { terms.screenSize >= it.ssMin && terms.screenSize <= it.ssMax }.toMutableList())
                if (master.lens.isNotEmpty()) modelLensMap[it.model] = master
                master.lens.isNotEmpty()
            }.toMutableList()
            if (terms.throwDistance?.isNotNull() == true) {
                modelTableList = filterThrowDistance(modelTableList, terms.screenSize,
                    terms.throwDistance!!
                )
            }
        } else {
            modelTableList.forEach {
//                modelLensMap[it.model] = DB.findMasterData(it)
                val map = DB.findMasterData(it)
                if (map is MasterDataEntity) {
                    it.model?.let { it1 -> modelLensMap.put(it1, map) }
//                    modelLensMap[it.model] = map
                }
            }
        }
        dataSource.clear()
        dataSource.addAll(modelTableList)
        mRecyclerView.adapter.notifyDataSetChanged()
    }

    private fun filterThrowDistance(list: MutableList<ModelTableEntity>, screenSize: Int, distance: ThrowDistance): MutableList<ModelTableEntity> {
        return list.filter {
            var master = DB.findMasterData(it)!!
            master = master.copy(lens = master.lens.filter {
                var end = if (it.wideA > 0) screenSize * it.wideA + it.wideB else 0F
                var start = if (it.teleA > 0) screenSize * it.teleA + it.teleB else end
                if (it.lToF > 0) {
                    end -= it.lToF
                    start -= it.lToF
                }
                screenSize >= it.ssMin && screenSize <= it.ssMax && !(start < distance.min() || end > distance.max())
            }.toMutableList())
            if (master.lens.isNotEmpty()) modelLensMap[it.model] = master
            master.lens.isNotEmpty()
        }.toMutableList()
    }
}

private operator fun <K, V> MutableMap<K, V>.set(model: K?, value: V) {

}

enum class Condition {
    Equal, Above, Below, Between
}

data class SearchTerms(var query: String? = "", var category: String? = "", var brightness: Brightness? = Brightness(), var resolution: String? = "", var screenSize: Int = 0, var throwDistance: ThrowDistance? = ThrowDistance(), var unit: Float = CM) : Parcelable {

    constructor(parcel: Parcel) : this(
            parcel.readString(),
            parcel.readString(),
            parcel.readParcelable(Brightness::class.java.classLoader),
            parcel.readString(),
            parcel.readInt(),
            parcel.readParcelable(ThrowDistance::class.java.classLoader),
            parcel.readFloat())

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(query)
        parcel.writeString(category)
        parcel.writeParcelable(brightness, flags)
        parcel.writeString(resolution)
        parcel.writeInt(screenSize)
        parcel.writeParcelable(throwDistance, flags)
        parcel.writeFloat(unit)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<SearchTerms> {
        override fun createFromParcel(parcel: Parcel): SearchTerms {
            return SearchTerms(parcel)
        }

        override fun newArray(size: Int): Array<SearchTerms?> {
            return arrayOfNulls(size)
        }
    }

}

data class Brightness(var min: Int = 0, var max: Int = 0, var condition: Condition? = Condition.Equal) : Parcelable {
    constructor(parcel: Parcel) : this(parcel.readInt(), parcel.readInt(),
        parcel.readString()?.let { Condition.valueOf(it) })

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(min)
        parcel.writeInt(max)
        parcel.writeString(condition?.name)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<Brightness> {
        override fun createFromParcel(parcel: Parcel): Brightness {
            return Brightness(parcel)
        }

        override fun newArray(size: Int): Array<Brightness?> {
            return arrayOfNulls(size)
        }
    }

    fun isNotNull() = !(min == 0 && max == 0 && condition == Condition.Equal)
}

data class ThrowDistance(var min: Float = 0F, var max: Float = 0F, var condition: Condition? = Condition.Equal, var unit: TypeUnit? = TypeUnit.CM) : Parcelable {

    constructor(parcel: Parcel) : this(parcel.readFloat(), parcel.readFloat(),
        parcel.readString()?.let { Condition.valueOf(it) },
        parcel.readString()?.let { TypeUnit.valueOf(it) })

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeFloat(min)
        parcel.writeFloat(max)
        parcel.writeString(condition?.name)
        parcel.writeString(unit?.name)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<Brightness> {
        override fun createFromParcel(parcel: Parcel): Brightness {
            return Brightness(parcel)
        }

        override fun newArray(size: Int): Array<Brightness?> {
            return arrayOfNulls(size)
        }
    }

    fun min() = min * (if (unit == TypeUnit.INCH) FT else unit?.value)!!
    fun max() = max * (if (unit == TypeUnit.INCH) FT else unit?.value)!!

    fun isNotNull() = !(min == 0F && max == 0F && condition == Condition.Equal && unit == TypeUnit.CM)
}
package com.epson.htdc.config

import android.content.Context
import android.os.Parcel
import android.os.Parcelable
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alibaba.fastjson.annotation.JSONField
import java.util.ArrayList

/**
 * Create by chimis(<EMAIL>) on 2018/2/8
 */
object DB {

    val masterDataList = arrayListOf<MasterDataEntity>()
    val modelTableList = arrayListOf<ModelTableEntity>()
    val resolutionList = arrayListOf<String>()

    fun init(context: Context) {
        masterDataList.clear()
        modelTableList.clear()
        resolutionList.clear()
        context.assets.apply {
            val deviceTableList = JSON.parseArray(open("device_table.json").bufferedReader().use { it.readText() }).toJavaList(JSONObject::class.java)
            masterDataList.addAll(JSON.parseArray(open("master_data.json").bufferedReader().use { it.readText() }, MasterDataEntity::class.javaObjectType))

//            open("model_table.json").bufferedReader().use { it.readText() }.let { string ->
//
//                JSON.parseArray(string).apply {
//                    forEach { json ->
//                        (json as JSONObject).apply {
//                            deviceTableList.firstOrNull { getString("model") == it.getString("model") }?.mapValues { (key, value) ->
//
//                                println(key + "------"+value)
//
//
//
//                            }
//
//                            println("+++++++++++++++++++++++++++++++++++++")
//                        }
//                    }
//                }
//                println("--------------------------------------")
//            }
//            JSON.parseArray(open("model_table.json").bufferedReader().use { it.readText() }.let { string ->
//
//                var jsonValue = JSON.parseArray(string).apply {
//                    forEach { json ->
//                        (json as JSONObject).apply {
//                            deviceTableList.firstOrNull { getString("model") == it.getString("model") }?.mapValues { (key, value) ->
//                                put(key, value)
//                            }
//                        }
//                    }
//                }.toJSONString()
//
//            }, ModelTableEntity::class.javaObjectType)

            modelTableList.addAll(JSON.parseArray(open("model_table.json").bufferedReader().use { it.readText() }.let { string ->
                JSON.parseArray(string).apply {
                    forEach { json ->
                        (json as JSONObject).apply {
                            deviceTableList.firstOrNull { getString("model") == it.getString("model") }?.mapValues { (key, value) ->
                                put(key, value)
                            }
                        }
                    }
                }.toJSONString()
            }, ModelTableEntity::class.javaObjectType))
            modelTableList.forEach { model ->
                val resolution = model.data?.replace("(.+_)|(([0-9]|\\.)+$)".toRegex(), "")
                if (!resolutionList.contains(resolution)) {
                    if (resolution != null) {
                        resolutionList.add(resolution)
                    }
                }
            }
        }
    }

    fun findMasterData(model: ModelTableEntity) = masterDataList.find {it.data == model.data}
}

/***************************************************************************************************
 * 数据实体 Entity
 ***************************************************************************************************/
//模型表
data class ModelTableEntity(
    @JSONField(name = "model") val model: String?,
    @JSONField(name = "brightness") val brightness: Int,
    @JSONField(name = "data") val data: String?,
    @JSONField(name = "category") val category: String?,
    @JSONField(name = "scld") val colorBrightness: String?,
    @JSONField(name = "fbl") val resolution: String?,
    @JSONField(name = "dbd") val contrastRatio: String?,
    @JSONField(name = "zl") val weight: String?,
    @JSONField(name = "jggy") val laserLightSource: Boolean,
    @JSONField(name = "kghjt") val replaceableLens: Boolean,
    @JSONField(name = "szhd") val fingerInteraction: Boolean
) : Parcelable {
    constructor(parcel: Parcel) : this(
            parcel.readString(),
            parcel.readInt(),
            parcel.readString(),
            parcel.readString(),
            parcel.readString(),
            parcel.readString(),
            parcel.readString(),
            parcel.readString(),
            parcel.readByte() != 0.toByte(),
            parcel.readByte() != 0.toByte(),
            parcel.readByte() != 0.toByte())

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(model)
        parcel.writeInt(brightness)
        parcel.writeString(data)
        parcel.writeString(category)
        parcel.writeString(colorBrightness)
        parcel.writeString(resolution)
        parcel.writeString(contrastRatio)
        parcel.writeString(weight)
        parcel.writeByte(if (laserLightSource) 1 else 0)
        parcel.writeByte(if (replaceableLens) 1 else 0)
        parcel.writeByte(if (fingerInteraction) 1 else 0)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<ModelTableEntity> {
        override fun createFromParcel(parcel: Parcel): ModelTableEntity {
            return ModelTableEntity(parcel)
        }

        override fun newArray(size: Int): Array<ModelTableEntity?> {
            return arrayOfNulls(size)
        }
    }

}

//主数据
data class MasterDataEntity(
    @JSONField(name = "data") val data: String?,
    @JSONField(name = "res_x") val resX: Int,
    @JSONField(name = "res_y") val resY: Int,
    @JSONField(name = "d_zoom") val dZoom: Float,
    @JSONField(name = "p_offset") val pOffset: Float,
    @JSONField(name = "lens") val lens: MutableList<LensEntity>
) : Parcelable {
    constructor(parcel: Parcel) : this(
            parcel.readString(),
            parcel.readInt(),
            parcel.readInt(),
            parcel.readFloat(),
            parcel.readFloat(),
            parcel.createTypedArrayList(LensEntity)!!.toMutableList())

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(data)
        parcel.writeInt(resX)
        parcel.writeInt(resY)
        parcel.writeFloat(dZoom)
        parcel.writeFloat(pOffset)
        parcel.writeTypedList(lens)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<MasterDataEntity> {
        override fun createFromParcel(parcel: Parcel): MasterDataEntity {
            return MasterDataEntity(parcel)
        }

        override fun newArray(size: Int): Array<MasterDataEntity?> {
            return arrayOfNulls(size)
        }
    }
}
//镜头
data class LensEntity(
    @JSONField(name = "name") val name: String?,
    @JSONField(name = "wide_a") val wideA: Float,
    @JSONField(name = "wide_b") val wideB: Float,
    @JSONField(name = "tele_a") val teleA: Float,
    @JSONField(name = "tele_b") val teleB: Float,
    @JSONField(name = "ls_vumax") val lsVuMax: Float,
    @JSONField(name = "ls_vumin") val lsVuMin: Float,
    @JSONField(name = "ls_vdmax") val lsVdMax: Float,
    @JSONField(name = "ls_vdmin") val lsVdMin: Float,
    @JSONField(name = "ls_hmax") val lsHMax: Float,
    @JSONField(name = "ls_hmin") val lsHMin: Float,
    @JSONField(name = "ss_min") val ssMin: Int,
    @JSONField(name = "ss_max") val ssMax: Int,
    @JSONField(name = "offset") val offset: Double,
    @JSONField(name = "b_to_a") val bToA: Float,
    @JSONField(name = "l_to_f") val lToF: Float,
    @JSONField(name = "type") val type: Int
) : Parcelable {
    constructor(parcel: Parcel) : this(
            parcel.readString(),
            parcel.readFloat(),
            parcel.readFloat(),
            parcel.readFloat(),
            parcel.readFloat(),
            parcel.readFloat(),
            parcel.readFloat(),
            parcel.readFloat(),
            parcel.readFloat(),
            parcel.readFloat(),
            parcel.readFloat(),
            parcel.readInt(),
            parcel.readInt(),
            parcel.readDouble(),
            parcel.readFloat(),
            parcel.readFloat(),
            parcel.readInt())

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(name)
        parcel.writeFloat(wideA)
        parcel.writeFloat(wideB)
        parcel.writeFloat(teleA)
        parcel.writeFloat(teleB)
        parcel.writeFloat(lsVuMax)
        parcel.writeFloat(lsVuMin)
        parcel.writeFloat(lsVdMax)
        parcel.writeFloat(lsVdMin)
        parcel.writeFloat(lsHMax)
        parcel.writeFloat(lsHMin)
        parcel.writeInt(ssMin)
        parcel.writeInt(ssMax)
        parcel.writeDouble(offset)
        parcel.writeFloat(bToA)
        parcel.writeFloat(lToF)
        parcel.writeInt(type)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<LensEntity> {
        override fun createFromParcel(parcel: Parcel): LensEntity {
            return LensEntity(parcel)
        }

        override fun newArray(size: Int): Array<LensEntity?> {
            return arrayOfNulls(size)
        }
    }
}
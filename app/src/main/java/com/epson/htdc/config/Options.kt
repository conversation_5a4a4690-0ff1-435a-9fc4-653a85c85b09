package com.epson.htdc.config

import com.epson.htdc.MainActivity
import com.epson.htdc.config.GL.FT
import com.epson.htdc.config.GL.INCH
import com.epson.htdc.config.GL.M
import kotlin.math.atan
import kotlin.math.cos
import kotlin.math.roundToInt
import kotlin.math.sin

object Options {

    enum class UpdateType {
        ALL, ORIGINAL, CALCULATED
    }

    enum class InstallType {
        DESKTOP, CELLING
    }

    lateinit var model: ModelTableEntity
    lateinit var master: MasterDataEntity
    lateinit var len: LensEntity
    var original: ScreenParameter = ScreenParameter()
    var calculated: ScreenParameter = ScreenParameter()
    var projectorAspectRatio = 0F
    var screenAspectRatio = 0F
    var optionsUpdateType = UpdateType.ORIGINAL
    var throwDistance = Pair(0F, 0F)
    var unit = TypeUnit.CM
    var isFlexCellingType = false
    var isSupportLensChecker = false
    var installType = InstallType.DESKTOP
    var isUsedRoomInformation = false
    var isUsedSettingPlate = false
    var extraCellingHeight = 260F
    var extraFloorToScreenHeight = 50F
    var py = 30

    fun init(activity: MainActivity) {
        model = activity.intent.getParcelableExtra(GL.ARGUMENTS_KEY)!!
        master = activity.intent.getParcelableExtra(GL.ARGUMENTS_KEY_EXTRA)!!
        len = master.lens.first()
        isSupportLensChecker = len.lsVuMax > 0
        isFlexCellingType = (len.type - len.type % 100) / 100 == 1
        installType = if (isFlexCellingType) InstallType.CELLING else InstallType.DESKTOP
        projectorAspectRatio = master.resY / master.resX.toFloat()
        screenAspectRatio = projectorAspectRatio
        val size = activity.intent.getFloatExtra(GL.ARGUMENTS_KEY_SCREEN_SIZE, 0F)
        if (size > 0) {
            setOriginal(size)
        }
    }

    fun setAspectRatio(aspect: Float) {
        val aspectWidth = original.size * cos(atan(aspect)) * GL.INCH
        screenAspectRatio = aspect
        original.setOriginal(original.size, aspectWidth, aspectWidth * aspect)
        if (master.dZoom > 1) {
            calculated.setCurrent(original.size)
        } else {
            calculated.setCurrent(-1F)
        }
        throwDistance = Pair(original.currentSize * len.wideA + len.wideB, original.currentSize * len.teleA + len.teleB)
    }

    private fun setOriginal(size: Float) {
        val width = size * cos(atan(Options.screenAspectRatio)) * INCH
        val height = width * Options.screenAspectRatio
        original.setOriginal(size, width, height)
        calculated.setCurrent(size)
        optionsUpdateType = UpdateType.ORIGINAL
        throwDistance = Pair(original.currentSize * len.wideA + len.wideB, original.currentSize * len.teleA + len.teleB)
    }

    fun setOriginal(size: Float, width: Float, height: Float) {
        original.setOriginal(size, width, height)
        calculated.setCurrent(size)
        optionsUpdateType = UpdateType.ORIGINAL
        throwDistance = Pair(original.currentSize * len.wideA + len.wideB, original.currentSize * len.teleA + len.teleB)
    }

    fun setDistance(value: Int) {
        throwDistance = Pair(value + len.lToF, 0F)
        val sizeWide = if (len.wideA > 0) (throwDistance.first - len.wideB) / len.wideA else 0F
        val sizeTele = if (len.teleA > 0) (throwDistance.first - len.teleB) / len.teleA else 0F
        original.setCurrent(sizeWide)
        calculated.setCurrent(sizeTele)
        when {
            sizeWide > 0 && sizeTele > 0 -> optionsUpdateType = UpdateType.ALL
            sizeWide > 0 -> optionsUpdateType = UpdateType.ORIGINAL
            sizeTele > 0 -> optionsUpdateType = UpdateType.CALCULATED
        }
    }

    fun destroy() {
        original = ScreenParameter()
        calculated = ScreenParameter()
        projectorAspectRatio = 0F
        screenAspectRatio = 0F
        optionsUpdateType = UpdateType.ORIGINAL
        throwDistance = Pair(0F, 0F)
        unit = TypeUnit.CM
        isFlexCellingType = false
        installType = InstallType.DESKTOP
        isUsedRoomInformation = false
        isUsedSettingPlate = false
        extraCellingHeight = 260F
        extraFloorToScreenHeight = 50F
        py = 30
    }

}

fun distance(value: Float) = display(value - Options.len.lToF)

fun display(value: Float) = when (Options.unit) {
    TypeUnit.CM -> if (Options.isFlexCellingType) value.smart() else value.roundToInt().toString()
    TypeUnit.M -> (value / M).smart(true)
    TypeUnit.INCH -> cmToInch(value)
}

fun cmToInch(value: Float): String {
    val ft = (value / FT).toInt()
    val inch = (value - ft * FT) / INCH
    return if (ft > 0) "$ft\' ${inch.smart()}\""
    else "${inch.smart()}\""
}

data class ScreenParameter(var size: Float = 0F, var width: Float = 0F, var height: Float = 0F, var currentSize: Float = 0F, var currentWidth: Float = 0F, var currentHeight: Float = 0F) {

    fun setOriginal(sizeValue: Float, widthValue: Float, heightValue: Float) {
        this.size = sizeValue
        this.width = widthValue
        this.height = heightValue
        when {
            Options.projectorAspectRatio < Options.screenAspectRatio -> {
                currentHeight = height
                currentWidth = currentHeight / Options.projectorAspectRatio
                currentSize = currentHeight / sin(atan(Options.projectorAspectRatio)) / GL.INCH
            }
            Options.projectorAspectRatio > Options.screenAspectRatio -> {
                currentWidth = width
                currentHeight = currentWidth * Options.projectorAspectRatio
                currentSize = currentWidth / cos(atan(Options.projectorAspectRatio)) / GL.INCH
            }
            else -> {
                currentSize = size
                currentWidth = width
                currentHeight = height
            }
        }
    }

    fun setCurrent(sizeValue: Float) {
        currentSize = sizeValue
        currentWidth = sizeValue * cos(atan(Options.projectorAspectRatio)) * GL.INCH
        currentHeight = currentWidth * Options.projectorAspectRatio
        when {
            Options.projectorAspectRatio < Options.screenAspectRatio -> {
                height = currentHeight
                width = height / Options.screenAspectRatio
                size = height / sin(atan(Options.screenAspectRatio)) / GL.INCH
            }
            Options.projectorAspectRatio > Options.screenAspectRatio -> {
                width = currentWidth
                height = width * Options.screenAspectRatio
                size = width / cos(atan(Options.screenAspectRatio)) / GL.INCH
            }
            else -> {
                size = currentSize
                width = currentWidth
                height = currentHeight
            }
        }
    }
}

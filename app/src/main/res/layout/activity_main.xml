<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?colorBackgroundLite"
        android:orientation="vertical">

        <android.support.v7.widget.Toolbar
            android:id="@+id/mToolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?colorPrimary"
            android:elevation="3dp"
            app:contentInsetStartWithNavigation="0dp"
            app:navigationIcon="@drawable/ic_back"
            app:subtitleTextColor="?colorPrimaryText" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?colorBackground"
            android:elevation="3dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:paddingBottom="8dp"
                    android:paddingEnd="16dp"
                    android:paddingStart="16dp"
                    android:paddingTop="8dp"
                    android:text="屏幕尺寸"
                    android:textColor="?colorContent" />

                <TextView
                    android:id="@+id/mOptionScreenSize"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableEnd="@drawable/ic_edit"
                    android:drawablePadding="8dp"
                    android:paddingBottom="8dp"
                    android:paddingEnd="16dp"
                    android:paddingStart="16dp"
                    android:paddingTop="8dp"
                    android:text="设置"
                    android:textColor="?colorContent" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:paddingBottom="8dp"
                    android:paddingEnd="16dp"
                    android:paddingStart="16dp"
                    android:paddingTop="8dp"
                    android:text="屏幕宽高"
                    android:textColor="?colorContent" />

                <TextView
                    android:id="@+id/mOptionScreenWidthAndHeight"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:drawableEnd="@drawable/ic_edit"
                    android:drawablePadding="8dp"
                    android:paddingBottom="8dp"
                    android:paddingEnd="8dp"
                    android:paddingStart="16dp"
                    android:paddingTop="8dp"
                    android:text="设置"
                    android:textColor="?colorContent"
                    android:textColorHint="?colorContent" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/mThrowDistanceTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:paddingBottom="8dp"
                    android:paddingEnd="16dp"
                    android:paddingStart="16dp"
                    android:paddingTop="8dp"
                    android:text="投影距离（L）"
                    android:textColor="?colorContent" />

                <TextView
                    android:id="@+id/mThrowDistance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableEnd="@drawable/ic_edit"
                    android:drawablePadding="8dp"
                    android:paddingBottom="8dp"
                    android:paddingEnd="16dp"
                    android:paddingStart="16dp"
                    android:paddingTop="8dp"
                    android:text="设置"
                    android:textColor="?colorContent" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/mDistanceOffsetTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:paddingBottom="8dp"
                    android:paddingEnd="16dp"
                    android:paddingStart="16dp"
                    android:paddingTop="8dp"
                    android:text="@string/distance_from_screen_edge"
                    android:textColor="?colorContent" />

                <TextView
                    android:id="@+id/mDistanceOffset"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:drawableEnd="@drawable/ic_edit"
                    android:drawablePadding="8dp"
                    android:enabled="false"
                    android:paddingBottom="8dp"
                    android:paddingEnd="8dp"
                    android:paddingStart="16dp"
                    android:paddingTop="8dp"
                    android:text="自动计算"
                    android:textColor="@color/color_edit"
                    android:textColorHint="@color/color_edit" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:paddingBottom="8dp"
                    android:paddingEnd="16dp"
                    android:paddingStart="16dp"
                    android:paddingTop="8dp"
                    android:text="屏前照度"
                    android:textColor="?colorContent" />

                <TextView
                    android:id="@+id/mIllumination"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:drawableEnd="@drawable/ic_edit"
                    android:drawablePadding="8dp"
                    android:enabled="false"
                    android:paddingBottom="8dp"
                    android:paddingEnd="8dp"
                    android:paddingStart="16dp"
                    android:paddingTop="8dp"
                    android:text="自动计算"
                    android:textColor="@color/color_edit"
                    android:textColorHint="@color/color_edit" />

            </LinearLayout>

        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:paddingBottom="12dp"
            android:paddingTop="12dp">

            <com.epson.htdc.config.ProjectorView
                android:id="@+id/mProjectorView"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
            
            <Button
                android:id="@+id/mLensShiftChecker"
                style="@style/Widget.AppCompat.Button.Borderless.Colored"
                android:layout_width="wrap_content"
                android:layout_height="30dp"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="8dp"
                android:layout_marginEnd="16dp"
                android:layout_marginStart="16dp"
                android:background="?selectableItemBackground"
                android:text="镜头位移检测器"
                android:textColor="?colorAction"
                android:textSize="12sp" />

        </RelativeLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingBottom="12dp"
                android:paddingEnd="16dp"
                android:paddingStart="16dp"
                android:paddingTop="12dp"
                android:text="屏幕纵横比" />

            <RadioGroup
                android:id="@+id/mScreenAspectRatioGroup"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <RadioButton
                    style="@style/RectRadio"
                    android:layout_width="72dp"
                    android:layout_height="wrap_content"
                    android:tag="0.75"
                    android:text="4:3" />

                <RadioButton
                    style="@style/RectRadio"
                    android:layout_width="72dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:tag="0.5625"
                    android:text="16:9" />

                <RadioButton
                    style="@style/RectRadio"
                    android:layout_width="72dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="8dp"
                    android:tag="0.625"
                    android:text="16:10" />

            </RadioGroup>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingBottom="12dp"
                android:paddingEnd="16dp"
                android:paddingStart="16dp"
                android:paddingTop="12dp"
                android:text="安装方式" />

            <RadioGroup
                android:id="@+id/mInstallationTypeGroup"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/mInstallationTypeDesktop"
                    style="@style/RectRadio"
                    android:layout_width="72dp"
                    android:layout_height="wrap_content"
                    android:text="桌面" />

                <RadioButton
                    android:id="@+id/mInstallationTypeCelling"
                    style="@style/RectRadio"
                    android:layout_width="72dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="8dp"
                    android:text="吊顶" />

            </RadioGroup>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="52dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingBottom="12dp"
                android:paddingEnd="16dp"
                android:paddingStart="16dp"
                android:paddingTop="12dp"
                android:text="计量单位" />

            <RadioGroup
                android:id="@+id/mUnitGroup"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <RadioButton
                    style="@style/RectRadio"
                    android:layout_width="72dp"
                    android:layout_height="wrap_content"
                    android:tag="CM"
                    android:text="cm" />

                <RadioButton
                    style="@style/RectRadio"
                    android:layout_width="72dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:tag="M"
                    android:text="m" />

                <RadioButton
                    style="@style/RectRadio"
                    android:layout_width="72dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="8dp"
                    android:tag="INCH"
                    android:text="Ft/in" />

            </RadioGroup>
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:animateLayoutChanges="true"
        android:background="?colorBackground"
        android:clickable="true"
        android:elevation="12dp"
        android:focusable="true"
        android:longClickable="true"
        android:orientation="vertical">

        <TextView
            android:id="@+id/mExtraOptionsButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?selectableItemBackground"
            android:gravity="center"
            android:padding="12dp"
            android:text="附加选项"
            android:textColor="?colorContent" />

        <LinearLayout
            android:id="@+id/mExtraOptionsLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:paddingBottom="12dp"
                    android:paddingEnd="16dp"
                    android:paddingStart="16dp"
                    android:paddingTop="12dp"
                    android:text="使用房间信息" />

                <android.support.v7.widget.SwitchCompat
                    android:id="@+id/mUseRoomInformationSwitch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:padding="16dp"
                    android:text="吊顶高度（C）" />

                <TextView
                    android:id="@+id/mExtraOptionsCellingHeight"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableEnd="@drawable/ic_edit"
                    android:drawablePadding="8dp"
                    android:enabled="false"
                    android:padding="16dp"
                    android:textColor="@color/color_edit" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:padding="16dp"
                    android:text="地面到屏幕的高度（S）" />

                <TextView
                    android:id="@+id/mExtraOptionsFloorToScreenHeight"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableEnd="@drawable/ic_edit"
                    android:drawablePadding="8dp"
                    android:enabled="false"
                    android:padding="16dp"
                    android:textColor="@color/color_edit" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/mUseSettingPlateSwitchLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:padding="16dp"
                    android:text="使用背景板" />

                <android.support.v7.widget.SwitchCompat
                    android:id="@+id/mUseSettingPlateSwitch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp" />

            </LinearLayout>
        </LinearLayout>

    </LinearLayout>
</RelativeLayout>
